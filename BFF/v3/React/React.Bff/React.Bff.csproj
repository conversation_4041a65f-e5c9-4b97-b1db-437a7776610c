<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <SpaRoot>..\react.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:5173</SpaProxyServerUrl>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\react.client\react.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>8.*-*</Version>
    </PackageReference>
    <PackageReference Include="Duende.BFF" Version="3.0.0" />
    <PackageReference Include="Duende.BFF.Yarp" Version="3.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.1" />
  </ItemGroup>

</Project>
