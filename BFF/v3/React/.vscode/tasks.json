{
    "version": "2.0.0",
    "tasks": [
      {
        "label": "build",
        "type": "process",
        "command": "dotnet",
        "args": [
            "build",
            "${workspaceFolder}/React.sln",
            "/property:GenerateFullPaths=true",
            "/consoleloggerparameters:NoSummary"
        ],
        "problemMatcher": "$msCompile"
      },
      {
        "label": "build-api",
        "type": "process",
        "command": "dotnet",
        "args": [
            "build",
            "${workspaceFolder}/React.Api/React.Api.csproj",
            "/property:GenerateFullPaths=true",
            "/consoleloggerparameters:NoSummary"
        ],
        "problemMatcher": "$msCompile"
      },
      {
        "label": "build-bff",
        "type": "process",
        "command": "dotnet",
        "args": [
            "build",
            "${workspaceFolder}/React.Bff/React.Bff.csproj",
            "/property:GenerateFullPaths=true",
            "/consoleloggerparameters:NoSummary"
        ],
        "problemMatcher": "$msCompile"
      },
    ]

}