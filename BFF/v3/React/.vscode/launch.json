{
    "version": "0.2.0",
    "compounds": [
      {
        "name": "All",
        "configurations": ["API", "BFF"],
        "presentation": {
          "hidden": false,
          "group": "compunds",
        }
      },
    ],
    "configurations": [
        {
            "name": "API",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-api",
            "program": "${workspaceFolder}/React.Api/bin/Debug/net8.0/React.Api.dll",
            "args": [],
            "cwd": "${workspaceFolder}/React.Api",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "externalTerminal",
        },
        {
            "name": "BFF",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-bff",
            "program": "${workspaceFolder}/React.Bff/bin/Debug/net8.0/React.Bff.dll",
            "args": [],
            "cwd": "${workspaceFolder}/React.Bff",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "externalTerminal",
        },
    ]
}